## 1. Product Design Specification: "Customize" Page

### Overview & Goal
The "Customize" page is the creative hub for the Emoji Battery feature. After selecting a style from the gallery, users land here to fine-tune its appearance. The primary goal is to provide an intuitive and highly visual editing experience with a real-time preview, empowering users to create a personalized status bar icon that perfectly matches their style.

### Screen Layout & Components

#### **1.1. Header Bar**
*   **Component:** Standard App Bar.
*   **Elements:**
    *   **Back Navigation:** A standard back arrow icon on the left to return to the gallery.
    *   **Title:** Centered text displaying "Customize".

#### **1.2. Global Toggle**
*   **Component:** A container with a rounded rectangle shape and a light blue border.
*   **Content:**
    *   **Label:** "Enable or disable the emoji battery".
    *   **Control:** A standard toggle switch, visually indicating the global on/off state of the entire feature.

#### **1.3. Live Preview Area**
*   **Component:** A large, prominent `CardView` with rounded corners and a subtle, light pink background.
*   **Content:** This area displays a real-time, large-scale preview of the emoji battery.
    *   **Percentage Text:** Displays a sample battery level (e.g., "50%") on the left. The font size and color of this text must update in real-time based on user customizations.
    *   **Style Image:** Displays the main preview image for the selected style (the character and battery container together). This should be a high-quality image (`photo` field from Firebase).

#### **1.4. Style Selection**
This section allows the user to mix and match different battery containers and emoji characters.

*   **"Battery" Section:**
    *   **Component:** A horizontal `RecyclerView`.
    *   **Content:** A scrollable list of different battery container styles.
    *   **Interaction:** The currently selected style is highlighted with a blue border. Tapping another style updates the selection and the live preview.
    *   **Monetization:** Premium styles are marked with a diamond icon in the top-right corner.

*   **"Emoji" Section:**
    *   **Component:** A horizontal `RecyclerView`.
    *   **Content:** A scrollable list of different emoji/character styles.
    *   **Interaction:** Similar to the battery section, the selected emoji is highlighted and updates the live preview.
    *   **Monetization:** Premium emojis are also marked with a diamond icon.
    *   **"More >" Link:** A text link to navigate to a full-screen emoji gallery (potential future feature).

#### **1.5. Customization Controls**
*   **`Show Emoji` Toggle:**
    *   **Label:** "Show Emoji".
    *   **Control:** A toggle switch to show or hide the emoji/character in the preview and final overlay.
*   **`Percentage` Slider (Font Size):**
    *   **Label:** "Percentage".
    *   **Control:** A `SeekBar` that controls the font size of the percentage text.
    *   **Range:** Labeled "5dp" to "40dp".
*   **`Emoji Battery` Slider (Emoji Scale):**
    *   **Label:** "Emoji Battery".
    *   **Control:** A `SeekBar` that controls the scale (size) of the emoji relative to the battery container.
*   **`Show [Percentage]` Toggle:**
    *   **Label:** (Partially obscured) "Show...". Based on the PRD, this is "Show Percentage".
    *   **Control:** A toggle switch to show or hide the numerical percentage text.

#### **1.6. Call to Action**
*   **Component:** A full-width `Button` with a blue gradient background.
*   **Label:** "Apply".
*   **Action:** Saves the user's current configuration, enables the feature globally (if not already), and activates the `EmojiBatteryAccessibilityService` to display the overlay.

#### **1.7. Ad Banner**
*   **Component:** A standard banner ad container at the bottom of the screen.

---

## 2. Codebase Analysis & Implementation Plan

Your codebase has an excellent foundation for this screen. The MVI architecture is in place, but there are clear gaps between the current code and the UI design.

### What's Already Implemented (✅)

1.  **Core MVI Architecture:** `CustomizeFragment.kt`, `CustomizeViewModel.kt`, and `CustomizeState.kt` are all present. This is a massive head start.
2.  **State Management:** The `CustomizeState` data class already includes properties for most of the required states: `selectedStyle`, `showEmojiToggle`, `showPercentageToggle`, `percentageFontSize`, and `emojiSizeScale`.
3.  **Navigation:** The navigation from `EmojiBatteryFragment` to `CustomizeFragment` is implemented (`CustomizeFragment.newInstance(style)`), correctly passing the selected `BatteryStyle`.
4.  **Persistence Layer:** The `CustomizationRepository` (using DataStore) and the `SaveCustomizationUseCase` / `LoadCustomizationUseCase` are fully implemented. The "Apply" button has a solid backend to save its data to.
5.  **Basic UI Connections:** The `CustomizeFragment` has code to handle some click listeners (`applyButton`, `backNavigation`) and observe the ViewModel.
6.  **Adapter Foundation:** `BatteryStyleAdapter` is already created for the gallery and can be reused for the horizontal "Battery" and "Emoji" selection lists.

### What Needs to Be Implemented Next (🟡)

This is your roadmap to bridge the gap between the current code and the final design.

#### **Step 1: Build the UI Layout (XML)**
Check if the `fragment_emoji_customize.xml` layout needs to be updated to match the screenshot. This involves:
*    `MaterialCardView` for the live preview area.
*    `ImageView` for the preview image and `TextView` for the percentage.
*    two `RecyclerView`s for the "Battery" and "Emoji" selection lists.
*    the `SwitchMaterial` toggles and `Slider` components for customization.
*    the "Apply" `Button` and the ad banner container.
*   Connect all these views to the `FragmentEmojiCustomizeBinding`.

#### **Step 2: Implement the Live Preview**
*   In `CustomizeFragment.kt`, use Glide to load the main image into the preview `ImageView`. The URL comes from `state.selectedStyle.customizePreviewUrl`.
*   Bind the `CustomizeState` properties (`percentageFontSize`, `emojiSizeScale`, etc.) to the preview elements. When the state changes, update the `textSize` of the preview percentage, the scale of the preview emoji `ImageView`, and the visibility of the elements based on the toggles.

#### **Step 3: Define Data Strategy for Selection Lists (Critical Task)**
This is the most significant architectural challenge. The screenshot shows lists of alternative "Battery" and "Emoji" parts, but your `EmojiItemService` only fetches items for a single category at a time.

**Recommendation:**
1.  **For the "Emoji" List:** When a user enters the Customize screen for an item from `animal_category`, the ViewModel should **re-use `EmojiItemService`** to fetch all items from that same category (`animal_category`) to populate this list.
2.  **For the "Battery" List:** The battery containers appear generic. You should create a **new Remote Config key**, such as `"generic_battery_styles"`.
    *   Create a simple `GenericBatteryService` to fetch this list.
    *   These would be lightweight objects, perhaps with just an `id` and a `thumbnail` URL.
    *   When a user selects a new battery container, you would update the `batteryImageUrl` of the current `CustomizationConfig`.

#### **Step 4: Connect All UI Controls**
*   Wire up the `OnCheckedChangeListener` for the toggles (`Show Emoji`, `Show Percentage`) to call the corresponding `viewModel.handleEvent(...)`.
*   Wire up the `OnChangeListener` for the sliders (`Percentage` font size, `Emoji Battery` scale) to update the state in the `CustomizeViewModel`. The ViewModel already has the logic to coerce the values into valid ranges.

#### **Step 5: Implement Premium Flow**
*   When populating the "Battery" and "Emoji" `RecyclerView`s, pass the `isPremium` flag to the `BatteryStyleAdapter` to show the diamond icon.
*   In the adapter's click listener, if a user taps a premium item, instead of just updating the preview, trigger an event to the ViewModel (`CustomizeEvent.PremiumItemSelected`).
*   The ViewModel should then update the state to show a premium unlock dialog (similar to the logic in the gallery).

#### **Step 6: Final Polish**
*   Implement the `More >` click handler for the "Emoji" list to navigate to a dedicated emoji-only gallery if desired.
*   Implement the color picker UI and connect it to the `updatePercentageColor` event.
*   Ensure the "Apply" button's logic correctly saves the final, complete `CustomizationConfig`.
