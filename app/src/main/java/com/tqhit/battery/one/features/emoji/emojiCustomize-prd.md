## 1. Product Design Specification: "Customize" Page

### Overview & Goal
The "Customize" page is the creative hub for the Emoji Battery feature. After selecting a style from the gallery, users land here to fine-tune its appearance. The primary goal is to provide an intuitive and highly visual editing experience with a real-time preview, empowering users to create a personalized status bar icon that perfectly matches their style.

### Screen Layout & Components

#### **1.1. Header Bar**
*   **Component:** Standard App Bar.
*   **Elements:**
    *   **Back Navigation:** A standard back arrow icon on the left to return to the gallery.
    *   **Title:** Centered text displaying "Customize".

#### **1.2. Global Toggle**
*   **Component:** A container with a rounded rectangle shape and a light blue border.
*   **Content:**
    *   **Label:** "Enable or disable the emoji battery".
    *   **Control:** A standard toggle switch, visually indicating the global on/off state of the entire feature.

#### **1.3. Live Preview Area**
*   **Component:** A large, prominent `CardView` with rounded corners and a subtle, light pink background.
*   **Content:** This area displays a real-time, large-scale preview of the emoji battery.
    *   **Percentage Text:** Displays a sample battery level (e.g., "50%") on the left. The font size and color of this text must update in real-time based on user customizations.
    *   **Style Image:** Displays the main preview image for the selected style (the character and battery container together). This should be a high-quality image (`photo` field from Firebase).

#### **1.4. Style Selection**
This section allows the user to mix and match different battery containers and emoji characters.

*   **"Battery" Section:**
    *   **Component:** A horizontal `RecyclerView`.
    *   **Content:** A scrollable list of different battery container styles.
    *   **Interaction:** The currently selected style is highlighted with a blue border. Tapping another style updates the selection and the live preview.
    *   **Monetization:** Premium styles are marked with a diamond icon in the top-right corner.

*   **"Emoji" Section:**
    *   **Component:** A horizontal `RecyclerView`.
    *   **Content:** A scrollable list of different emoji/character styles.
    *   **Interaction:** Similar to the battery section, the selected emoji is highlighted and updates the live preview.
    *   **Monetization:** Premium emojis are also marked with a diamond icon.
    *   **"More >" Link:** A text link to navigate to a full-screen emoji gallery (potential future feature).

#### **1.5. Customization Controls**
*   **`Show Emoji` Toggle:**
    *   **Label:** "Show Emoji".
    *   **Control:** A toggle switch to show or hide the emoji/character in the preview and final overlay.
*   **`Percentage` Slider (Font Size):**
    *   **Label:** "Percentage".
    *   **Control:** A `SeekBar` that controls the font size of the percentage text.
    *   **Range:** Labeled "5dp" to "40dp".
*   **`Emoji Battery` Slider (Emoji Scale):**
    *   **Label:** "Emoji Battery".
    *   **Control:** A `SeekBar` that controls the scale (size) of the emoji relative to the battery container.
*   **`Show [Percentage]` Toggle:**
    *   **Label:** (Partially obscured) "Show...". Based on the PRD, this is "Show Percentage".
    *   **Control:** A toggle switch to show or hide the numerical percentage text.

#### **1.6. Call to Action**
*   **Component:** A full-width `Button` with a blue gradient background.
*   **Label:** "Apply".
*   **Action:** Saves the user's current configuration, enables the feature globally (if not already), and activates the `EmojiBatteryAccessibilityService` to display the overlay.

#### **1.7. Ad Banner**
*   **Component:** A standard banner ad container at the bottom of the screen.

---

## 2. Codebase Analysis & Implementation Plan

Your codebase has an excellent foundation for this screen. The MVI architecture is in place, but there are clear gaps between the current code and the UI design.

### What's Already Implemented (✅)

1.  **Core MVI Architecture:** `CustomizeFragment.kt`, `CustomizeViewModel.kt`, and `CustomizeState.kt` are all present. This is a massive head start.
2.  **State Management:** The `CustomizeState` data class already includes properties for most of the required states: `selectedStyle`, `showEmojiToggle`, `showPercentageToggle`, `percentageFontSize`, and `emojiSizeScale`.
3.  **Navigation:** The navigation from `EmojiBatteryFragment` to `CustomizeFragment` is implemented (`CustomizeFragment.newInstance(style)`), correctly passing the selected `BatteryStyle`.
4.  **Persistence Layer:** The `CustomizationRepository` (using DataStore) and the `SaveCustomizationUseCase` / `LoadCustomizationUseCase` are fully implemented. The "Apply" button has a solid backend to save its data to.
5.  **Basic UI Connections:** The `CustomizeFragment` has code to handle some click listeners (`applyButton`, `backNavigation`) and observe the ViewModel.
6.  **Adapter Foundation:** `BatteryStyleAdapter` is already created for the gallery and can be reused for the horizontal "Battery" and "Emoji" selection lists.

### What Needs to Be Implemented Next (🟡)

**Current State Assessment (Dec 2024):**
After reviewing the codebase, the MVI architecture, state management, and core functionality are already excellently implemented. The following structured implementation plan addresses the remaining gaps between current code and PRD requirements.

#### **Phase 1: Core UI Components (Tasks 1-3) ✅ COMPLETED**
**Objective**: Complete the missing essential UI elements

**Task 1: Add Global Toggle ✅ COMPLETED**
- **IMPLEMENTED**: Global "Enable or disable the emoji battery" toggle at top of screen
- **Implementation**: Added toggle to layout, connected to ViewModel's global state management
- **Files**: `fragment_emoji_customize.xml`, `CustomizeFragment.kt`, `CustomizeState.kt`
- **Status**: Working - properly syncs with CustomizationConfig.isGlobalEnabled

**Task 2: Implement Customization Sliders ✅ COMPLETED**
- **IMPLEMENTED**: Font size slider (5dp-40dp) and emoji scale slider (0.5x-2.0x) with real-time value display
- **Implementation**: Added sliders to layout, connected to existing ViewModel events with proper value conversion
- **Files**: `fragment_emoji_customize.xml`, `CustomizeFragment.kt`
- **Status**: Working - real-time updates with range validation and state synchronization

**Task 3: Enhance Live Preview with Image Loading ✅ COMPLETED**
- **IMPLEMENTED**: Glide integration for preview images following codebase patterns
- **Implementation**: Added `loadPreviewImages()` method with comprehensive error handling
- **Files**: `CustomizeFragment.kt`
- **Status**: Working - loads actual battery/emoji images with proper fallbacks

#### **Phase 2: Data Strategy & Style Selection (Tasks 4-5) 🔄 IN PROGRESS**
**Objective**: Populate the style selection RecyclerViews (currently empty)

**Task 4: Implement Battery Styles Data Strategy 🔄 NEXT**
- **MISSING**: Data source for battery container alternatives in horizontal RecyclerView
- **Implementation**: Create GenericBatteryService, add Remote Config key "generic_battery_styles"
- **Files**: New service class, `CustomizeViewModel.kt`
- **Priority**: High - Empty RecyclerView needs data source

**Task 5: Implement Emoji Styles Data Strategy 🔄 PENDING**
- **MISSING**: Emoji alternatives from same category in horizontal RecyclerView
- **Implementation**: Reuse `EmojiItemService` to fetch same-category items
- **Files**: `CustomizeViewModel.kt`
- **Priority**: High - Same-category alternatives for mixing/matching

#### **Phase 3: Advanced Features (Tasks 6-8)**
**Objective**: Complete premium features and integrations

**Task 6: Implement Premium Flow**
- **MISSING**: Premium unlock dialogs when tapping premium items
- **Implementation**: Integrate with existing premium infrastructure
- **Files**: `CustomizeFragment.kt`, `CustomizeViewModel.kt`

**Task 7: Add Color Picker**
- **MISSING**: Color picker UI for percentage text color (exists in state but no UI)
- **Implementation**: Color selection grid/wheel component
- **Files**: New color picker component, `CustomizeFragment.kt`

**Task 8: Ad Integration & Final Polish**
- **MISSING**: Banner ad integration, info dialog, success messages
- **Implementation**: Connect to `ApplovinBannerAdManager`, add dialogs
- **Files**: `CustomizeFragment.kt`

#### **Implementation Methodology (Per Task)**
1. **Code Implementation** - Following Android/Kotlin best practices per `.cursor/rules/kotlin.mdc`
2. **Compilation Verification** - Ensure no build errors after each change
3. **ADB Testing** - Use: `adb shell am start -n com.fc.p.tj.charginganimation.batterycharging.chargeeffect/com.tqhit.battery.one.activity.splash.SplashActivity`
4. **Logcat Monitoring** - Track with: `adb logcat | grep -E "(EmojiOverlay|EmojiService|EmojiView|EmojiPermission|EmojiAccessibility|EmojiCustomize)"`
5. **Functional Validation** - Verify each feature works end-to-end

#### **Architecture Strengths (Already Implemented ✅)**
- Complete MVI architecture with CustomizeFragment, CustomizeViewModel, CustomizeState
- Comprehensive state management with all required properties
- DataStore persistence layer with CustomizationRepository
- Navigation, lifecycle handling, error/loading states
- RecyclerView foundation with BatteryStyleAdapter

---

## **Current Implementation Status**

### **✅ Phase 1 Complete (100%)**
- **Global Toggle**: Working with proper state synchronization
- **Customization Sliders**: Font size (5dp-40dp) and emoji scale (0.5x-2.0x) functional
- **Live Preview**: Glide image loading with real-time updates

### **🔄 Phase 2 Active (0%)**
- **Next Task**: Implement Battery Styles Data Strategy (Task 4)
- **Current Issue**: Style selection RecyclerViews are empty and need data sources

### **⏳ Phase 3 Pending (0%)**
- Premium flow, color picker, ad integration awaiting Phase 2 completion

### **✅ Verification Notes**
From logcat analysis, the emoji overlay system is working excellently:
- Real-time battery updates (67% detected)
- Status bar integration functioning
- Emoji drawing and positioning accurate
- System icons (WiFi, cellular) properly displayed
- Full-width layout calculations working correctly

**Ready to proceed with Task 4: Battery Styles Data Strategy Implementation**
